package com.qf.auth.controller;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import com.qf.system.api.RemoteWxUserService;
import com.qf.system.api.RemoteUpdateUserByIdService;
import com.qf.system.api.domain.SysUser;
import com.qf.system.api.model.UserInfo;
import com.qf.common.core.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import com.qf.common.core.constant.SecurityConstants;
import com.qf.common.core.web.domain.AjaxResult;
import com.qf.common.redis.utils.RedisUtils;
import com.qf.system.api.RemoteLogService;
import com.qf.system.api.RemoteUserService;
import com.qf.common.core.utils.StringUtils;
import com.qf.common.core.vo.HttpResult;
import com.qf.auth.service.CommonService;
import org.springframework.web.bind.annotation.RequestParam;
import com.qf.common.core.enums.HttpErrorStatusCode;
import com.alibaba.fastjson.JSONObject;
import java.security.Principal;
import com.qf.auth.utils.HttpUtils;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.security.oauth2.provider.token.TokenStore;

/**
 * 自定义Oauth2获取令牌接口
 * <AUTHOR>
 */
@RefreshScope
@RestController
@RequestMapping("/oauth")
public class AuthController {

    @Autowired
    private TokenEndpoint tokenEndpoint;
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private CommonService commonService;
    
    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private RemoteLogService remoteLogService;
    
    @Autowired
    private RemoteWxUserService remoteWxUserService;
    
    @Autowired
    private RemoteUserService remoteUserService;
    
    @Autowired
    private RemoteUpdateUserByIdService	remoteUpdateUserByIdService;


    /**
     * Oauth2登录认证
     */
    @PostMapping("/token")
    public AjaxResult postAccessToken(Principal principal, @RequestParam Map<String, String> parameters) {
    	try{
    		/* 校验验证码 */
    		if(parameters.get("type").equals("system") || parameters.get("type").equals("mobile") || parameters.get("type").equals("wx_bd")){
                if (StringUtils.isEmpty(parameters.get("code")) || StringUtils.isEmpty(parameters.get("uuid")))
                {
                	return AjaxResult.error(HttpErrorStatusCode.VERIFICATION_CODE_NULL.getCode(), HttpErrorStatusCode.VERIFICATION_CODE_NULL.getInfo());
                }
                String verifyKey = Constants.CAPTCHA_CODE_KEY + parameters.get("uuid");
                if(!redisUtils.hasKey(verifyKey)){
                	return AjaxResult.error(HttpErrorStatusCode.VERIFICATION_CODE_TIME_OUT.getCode(),HttpErrorStatusCode.VERIFICATION_CODE_TIME_OUT.getInfo());
                }
                String captcha = redisUtils.get(verifyKey);
                if (!parameters.get("code").equalsIgnoreCase(captcha))
                {
                	return AjaxResult.error(HttpErrorStatusCode.VERIFICATION_CODE_ERROR.getCode(), HttpErrorStatusCode.VERIFICATION_CODE_ERROR.getInfo());
                }else{
                    redisUtils.del(verifyKey);
                }
    		}
    		/* 登录 */
            if(parameters.get("type").equals("system")){	// 登录后台管理系统
            	// 获取配置数据
            	Long timeOut = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeOut"));
            	parameters.put("privateKey", redisUtils.get(Constants.SYS_CONFIG_KEY+"privateKey"));
        		// 提交错误密码频率限制
                if( redisUtils.hasKey(Constants.FORBID_TIME+parameters.get("username")) ){
                	return AjaxResult.error(HttpErrorStatusCode.WRONG_PASSWORD_ERROR.getCode(), "提交错误密码频率超限，账号被锁，请在"+Long.toString(timeOut/3600)+"小时后再登录！");
                }
        		// 授权认证
            	OAuth2AccessToken oAuth2AccessToken = tokenEndpoint.postAccessToken(principal, parameters).getBody();
    			if(oAuth2AccessToken!=null){
    				commonService.delLoginRedis(parameters.get("username"));  // 清除redis
    	            return AjaxResult.success("",oAuth2AccessToken);
    			}else{
    				return AjaxResult.error(500, "未知错误，请联系系统管理员");
    			}
            }else if(parameters.get("type").equals("mobile")){	// 手机app登录
            	// 获取配置数据
            	Long timeOut = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeOut"));
            	parameters.put("privateKey", redisUtils.get(Constants.SYS_CONFIG_KEY+"privateKey"));
        		// 提交错误密码频率限制
                if( redisUtils.hasKey(Constants.FORBID_TIME+parameters.get("username")) ){
                	return AjaxResult.error(HttpErrorStatusCode.WRONG_PASSWORD_ERROR.getCode(), "提交错误密码频率超限，账号被锁，请在"+Long.toString(timeOut/3600)+"小时后再登录！");
                }else{
            		// 授权认证
                	OAuth2AccessToken oAuth2AccessToken = tokenEndpoint.postAccessToken(principal, parameters).getBody();
        			if(oAuth2AccessToken!=null){
        				commonService.delLoginRedis(parameters.get("username"));  // 清除redis
                    	int code = HttpServletResponse.SC_OK;
        	            return AjaxResult.success(code,"登录消息提示",oAuth2AccessToken);
        			}else{
        				return AjaxResult.error(500, "未知错误，请联系系统管理员");
        			}
                }
            }else if(parameters.get("type").equals("wx_bd")){	// 微信公众平台绑定登录
            	// 获取配置数据
            	Long timeOut = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeOut"));
            	parameters.put("privateKey", redisUtils.get(Constants.SYS_CONFIG_KEY+"privateKey"));
    			String openId     = parameters.get("openId");	// 微信公众平台openId
    			String headimgurl = parameters.get("headimgurl");	// 微信公众平台头像链接
        		// 提交错误密码频率限制
                if( redisUtils.hasKey(Constants.FORBID_TIME+parameters.get("username")) ){
                	return AjaxResult.error(HttpErrorStatusCode.WRONG_PASSWORD_ERROR.getCode(), "提交错误密码频率超限，账号被锁，请在"+Long.toString(timeOut/3600)+"小时后再登录！");
                }else{
            		// 授权认证
                	OAuth2AccessToken oAuth2AccessToken = tokenEndpoint.postAccessToken(principal, parameters).getBody();
        			if(oAuth2AccessToken!=null){
        				commonService.delLoginRedis(parameters.get("username"));  // 清除redis
                    	int code = HttpServletResponse.SC_OK;
        				// 微信公众平台相关
        				if(openId!=null && !openId.equals("")){
        					HttpResult<UserInfo> userResult = remoteUserService.getUserInfo(parameters.get("username"));
        					SysUser sysUserInfo = userResult.getData().getSysUser();
    						if(sysUserInfo.getWxId()==null || sysUserInfo.getWxId().equals("")){	// 没有被其它人使用过的微信号
        						String str = sysUserInfo.getUserId()+"~;~"+openId+"~;~"+headimgurl;
        						remoteUpdateUserByIdService.updateById(StringUtils.demical2Hex(StringUtils.stringToASCLL(StringUtils.replaceSpecialStr(str))));
    						}else{
    							if(openId.equals(sysUserInfo.getWxId())){
    								code = HttpErrorStatusCode.WX_BDD.getCode();	// 已经绑定过
    							}else{
    								code = HttpErrorStatusCode.WX_BDD_OTHER.getCode();	// 该微信号已经和其它账号绑定过了
    							}
    						}
        				}
        	            return AjaxResult.success(code,"登录消息提示",oAuth2AccessToken);
        			}else{
        				return AjaxResult.error(500, "登录失败，请稍后重试。");
        			}
                }
            }else if(parameters.get("type").equals("wx_jm")){	// 微信公众平台静默登录
            	String appid = redisUtils.get(Constants.SYS_CONFIG_KEY+"wxAppid");
                String secret = redisUtils.get(Constants.SYS_CONFIG_KEY+"wxSecret");
        		// 第一步：获取前端发来的code
        		String wcode = parameters.get("wcode");
        		// 第二步：获取token
                Map<String, String> keyMap = new HashMap<String, String>();
                keyMap.put("appid", appid);
                keyMap.put("secret", secret);
                keyMap.put("code", wcode);
                keyMap.put("grant_type", "authorization_code");
        		String getAccessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";
        		String encoding = "utf-8";
        		String accessTokenJsonStr = HttpUtils.send(getAccessTokenUrl, keyMap, encoding);
        		JSONObject accessTokenObg = JSONObject.parseObject(accessTokenJsonStr);
        		String accessToken = accessTokenObg.getString("access_token");
        		String openid = accessTokenObg.getString("openid");
        		// 第三步：静默登录
                HttpResult<SysUser> userResult = remoteWxUserService.findPageByOpenId(openid);
                if(userResult==null){
                	return AjaxResult.error(500, "登录失败，请稍后重试。");
                }else{
                    SysUser sysUserInfo = userResult.getData();
                    if(sysUserInfo==null){
                		// 获取用户信息
                		String getUerInfoUrl = "https://api.weixin.qq.com/sns/userinfo";
                        Map<String, String> ukeyMap = new HashMap<String, String>();
                        ukeyMap.put("access_token", accessToken);
                        ukeyMap.put("openid", openid);
                        ukeyMap.put("lang", "zh_CN");
                		String userInfoJsonStr = HttpUtils.send(getUerInfoUrl, ukeyMap, encoding);
                		JSONObject userInfoObg = JSONObject.parseObject(userInfoJsonStr);
                		String headimgurl = userInfoObg.getString("headimgurl");
                    	return AjaxResult.success(openid+"~;~"+headimgurl);	// 将各个参数发至前端
                    }else{
                    	parameters.put("username",sysUserInfo.getUserName());
                    	parameters.put("password",sysUserInfo.getPassword());
                		// 授权认证
                    	OAuth2AccessToken oAuth2AccessToken = tokenEndpoint.postAccessToken(principal, parameters).getBody();
            			if(oAuth2AccessToken!=null){
            				commonService.delLoginRedis(parameters.get("username"));  // 清除redis
            	            return AjaxResult.success("",oAuth2AccessToken);
            			}else{
            				return AjaxResult.error(500, "登录失败，请稍后重试。");
            			}
                    }
                }
            }else{
    			return AjaxResult.error(500, "无相关操作");
            }
    	}catch (Exception e) {
        	// 获取配置数据
        	Long timeOut = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeOut"));
			String errJsonStr = e.getMessage();
			int code = HttpServletResponse.SC_UNAUTHORIZED;	// 401鉴权错误
	    	if(e.getMessage().equals(HttpErrorStatusCode.USERNAME_NOT_FOUNT.getInfo())){	// 用户不存在
        		code = HttpErrorStatusCode.USERNAME_NOT_FOUNT.getCode();
        		errJsonStr = HttpErrorStatusCode.USERNAME_NOT_FOUNT.getInfo();
	    	}else if(e.getMessage().equals(HttpErrorStatusCode.USERNAME_DELETED.getInfo())){	// 用户已被删除
        		code = HttpErrorStatusCode.USERNAME_DELETED.getCode();
        		errJsonStr = HttpErrorStatusCode.USERNAME_DELETED.getInfo();
	    	}else if(e.getMessage().equals(HttpErrorStatusCode.USERNAME_DISABLE.getInfo())){	// 用户已停用
        		code = HttpErrorStatusCode.USERNAME_DISABLE.getCode();
        		errJsonStr = HttpErrorStatusCode.USERNAME_DISABLE.getInfo();
	    	}else if(e.getMessage().equals(HttpErrorStatusCode.WRONG_PASSWORD_ERROR.getInfo())){	// 密码错误
        		code = HttpErrorStatusCode.OVER_FREQUENCY_WRONG_PASSWORD_ERROR.getCode();
        		// 提交错误密码频率限制的数据记录
        		commonService.getWPassword(parameters.get("username"));
	            if( redisUtils.hasKey(Constants.FORBID_TIME+parameters.get("username")) ){
	            	errJsonStr = "提交错误密码频率超限，账号被锁，请在"+Long.toString(timeOut/3600)+"小时后再登录！";
	            }else{
	            	errJsonStr = "账号"+parameters.get("username")+"与密码不匹配！";
	            }
	        }
			return AjaxResult.error(code, errJsonStr);
    	}
    }

    @DeleteMapping("/logout")
    public HttpResult<?> logout(@RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String authHeader)
    {
        if (StringUtils.isEmpty(authHeader))
        {
            return HttpResult.ok();
        }
        String tokenValue = authHeader.replace(OAuth2AccessToken.BEARER_TYPE, StringUtils.EMPTY).trim();
        OAuth2AccessToken accessToken = tokenStore.readAccessToken(tokenValue);
        if (accessToken == null || StringUtils.isEmpty(accessToken.getValue()))
        {
            return HttpResult.ok();
        }
        // 清空 access token
        tokenStore.removeAccessToken(accessToken);
        // 清空 refresh token
        OAuth2RefreshToken refreshToken = accessToken.getRefreshToken();
        tokenStore.removeRefreshToken(refreshToken);
        Map<String, ?> map = accessToken.getAdditionalInformation();
        if (map.containsKey(SecurityConstants.DETAILS_USERNAME))
        {
            String username = (String) map.get(SecurityConstants.DETAILS_USERNAME);
            // 记录用户退出日志
            remoteLogService.saveLogininfor(username, Constants.LOGOUT, "退出成功");
        }
        return HttpResult.ok();
    }

}