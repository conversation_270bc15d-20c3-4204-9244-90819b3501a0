package com.qf.system.api.factory;



import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.RemoteUpdateUserByIdService;
import com.qf.system.api.domain.SysUser;

import feign.hystrix.FallbackFactory;



/**
 * 用户服务降级处理
 * 
 * <AUTHOR>   
 */
@Component
public class RemoteUpdateUserByIdFallbackFactory implements FallbackFactory<RemoteUpdateUserByIdService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUpdateUserByIdFallbackFactory.class);

    @Override
    public RemoteUpdateUserByIdService create(Throwable throwable)
    {
        log.error("用户数据更新失败:{}", throwable.getMessage());
        return new RemoteUpdateUserByIdService()
        {
            @Override
            public HttpResult<SysUser> updateById(String str)
            {
                return null;
            }
        };
    }
}
