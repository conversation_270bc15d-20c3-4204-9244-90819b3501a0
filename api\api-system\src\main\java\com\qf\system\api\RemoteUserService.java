package com.qf.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import com.qf.common.core.constant.ServiceNameConstants;
import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.factory.RemoteUserFallbackFactory;
import com.qf.system.api.model.UserInfo;

/**
 * 用户服务
 * 
 * <AUTHOR>   
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
	
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
	@PostMapping(value = "/user/info/{username}")
    public HttpResult<UserInfo> getUserInfo(@PathVariable("username") String username);
    
}