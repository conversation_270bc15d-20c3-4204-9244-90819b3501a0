<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="services-monitor" />
        <module name="common-security" />
        <module name="services-system" />
        <module name="data" />
        <module name="auth" />
        <module name="services-dev" />
        <module name="common-log" />
        <module name="common-datascope" />
        <module name="common-redis" />
        <module name="captcha" />
        <module name="api-system" />
        <module name="services-task" />
        <module name="services-mobile" />
        <module name="gateway" />
        <module name="common-core" />
      </profile>
    </annotationProcessing>
  </component>
</project>