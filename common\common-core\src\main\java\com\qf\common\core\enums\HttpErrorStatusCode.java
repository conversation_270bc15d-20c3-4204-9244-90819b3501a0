package com.qf.common.core.enums;



/**
 * HTTP错误状态码
 * 
 * <AUTHOR>   
 */
public enum HttpErrorStatusCode
{
	VERIFICATION_CODE_NULL(210, "验证码不能为空。"),
	VERIFICATION_CODE_TIME_OUT(211, "验证码已过期或无此验证码。"),
	VERIFICATION_CODE_ERROR(212, "验证码错误。"),
	
	PASSWORD_ERROR(213, "密码错误。"), 
	OVER_FREQUENCY_WRONG_PASSWORD_ERROR(214, "错误密码提交过频错误"),
	WRONG_PASSWORD_ERROR(215, "用户名或密码错误"),
	USERNAME_NOT_FOUNT(216, "用户名不存在"),
	USERNAME_DELETED(217, "该账号已被删除"),
	USERNAME_DISABLE(218, "该账号已停用"),
	PARAMETER_ERROR(219, "参数校验错误"),
	
	LOGIN_FAIL(510, "服务器内部错误，登录失败"),
	WX_BDD(700, "已经绑定过微信公众平台"),
	WX_BDD_OTHER(800, "该微信号已经和其它账号绑定过了");
	
	
	
    private final int code;
    private final String info;

    HttpErrorStatusCode(int code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public int getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}