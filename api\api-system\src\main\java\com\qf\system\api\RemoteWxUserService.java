package com.qf.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.domain.SysUser;
import com.qf.common.core.constant.ServiceNameConstants;
import com.qf.system.api.factory.RemoteWxUserFallbackFactory;

/**
 * 用户服务
 * 
 * <AUTHOR>   
 */
@FeignClient(contextId = "RemoteWxUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteWxUserFallbackFactory.class)
public interface RemoteWxUserService
{
    
    /**
     * 通过微信公众平台openId查询用户信息
     *
     * @param openId 微信公众平台openId
     * @return 结果
     */
	@PostMapping(value = "/user/infoByOpenId/{openId}")
    public HttpResult<SysUser> findPageByOpenId(@PathVariable("openId") String openId);
    
}