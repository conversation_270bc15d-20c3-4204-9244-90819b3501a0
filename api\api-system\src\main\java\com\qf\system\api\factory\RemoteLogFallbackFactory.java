package com.qf.system.api.factory;



import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.RemoteLogService;
import com.qf.system.api.domain.SysOperLog;

import feign.hystrix.FallbackFactory;



/**
 * 日志服务降级处理
 * 
 * <AUTHOR>   
 */
@Component
public class RemoteLogFallbackFactory implements FallbackFactory<RemoteLogService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteLogFallbackFactory.class);

    @Override
    public RemoteLogService create(Throwable throwable)
    {
        log.error("日志服务调用失败:{}", throwable.getMessage());
        return new RemoteLogService()
        {
            @Override
            public HttpResult<Boolean> saveLog(SysOperLog sysOperLog)
            {
                return null;
            }

            @Override
            public HttpResult<Boolean> saveLogininfor(String username, String status, String message)
            {
                return null;
            }
        };
    }
}