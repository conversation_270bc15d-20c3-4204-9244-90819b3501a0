package com.qf.captcha.config;

import java.util.Properties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;

/**
 * 验证码配置
 * 
 * <AUTHOR>   
 */
@Configuration
public class CaptchaConfig
{
	
    @Bean(name = "captchaProducerMath")
    public DefaultKaptcha getKaptchaBeanMath()
    {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 是否有边框 
        properties.setProperty("kaptcha.border", "yes");
        // 验证码图片宽度
        properties.setProperty("kaptcha.image.width", "170");
        // 验证码图片高度
        properties.setProperty("kaptcha.image.height", "60");
        // 验证码文本字符大小
        properties.setProperty("kaptcha.textproducer.font.size", "45");
        // KAPTCHA_SESSION_KEY
        properties.setProperty("kaptcha.session.key", "kaptchaCodeMath");
        // 验证码文本字符间距
        properties.setProperty("kaptcha.textproducer.char.space", "3");
        // 验证码文本字符长度
        properties.setProperty("kaptcha.textproducer.char.length", "5");
        // 干扰相关
        properties.setProperty("kaptcha.noise.impl","com.google.code.kaptcha.impl.DefaultNoise");
        properties.setProperty("kaptcha.noise.color","blue");
        properties.setProperty("kaptcha.obscurificator.impl","com.google.code.kaptcha.impl.ShadowGimpy");
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }
    
}