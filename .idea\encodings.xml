<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/api/api-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/api/api-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/auth/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/auth/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/captcha/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/captcha/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-datascope/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-datascope/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/dev/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/dev/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/mobile/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/mobile/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/task/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/services/task/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>