package com.qf.auth.config;

import com.qf.common.core.utils.RSAUtils;
import com.qf.common.core.utils.StringUtils;
import com.qf.common.security.utils.SecurityUtils;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * 身份验证提供者
 * <AUTHOR>
 */
public class MyAuthenticationProvider extends DaoAuthenticationProvider {
	    
    public MyAuthenticationProvider(UserDetailsService userDetailsService) {
        setUserDetailsService(userDetailsService);
    }

    @Override
	protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
    	try{
        	if (authentication.getCredentials() == null) {
    			throw new BadCredentialsException(messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
    		}
    		String presentedPassword = authentication.getCredentials().toString();	// 登录时传过来的密码
    		String type = StringUtils.getMap(authentication.getDetails().toString()).get("type");
    		if(!type.equals("wx_jm")){
            	String privateKey = StringUtils.getMap(authentication.getDetails().toString()).get("privateKey");
        		String nowPassword = RSAUtils.decrypt(presentedPassword,RSAUtils.getPrivateKey(privateKey));	// rsa解密
        		// 校验密码
        		if ( !SecurityUtils.matchesPassword(nowPassword,userDetails.getPassword()) ) {
        			throw new BadCredentialsException(messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
        		}
    		}
		}catch (Exception e) {
			throw new BadCredentialsException(messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
	    }
	}

}