package com.qf.captcha.controller;



import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import com.qf.common.core.web.domain.AjaxResult;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.imageio.ImageIO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import com.google.code.kaptcha.Producer;
import com.qf.common.core.constant.Constants;
import com.qf.common.core.exception.CaptchaException;
import com.qf.common.core.utils.IdUtils;
import com.qf.common.core.utils.sign.Base64;
import com.qf.common.redis.utils.RedisUtils;



/**
 * 验证码处理
 */
@RestController
@RequestMapping("/getData")
public class CaptchaController {

    @Autowired
    private Producer producer;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 获取验证码接口
     */
    @PostMapping("/getCode")
    public AjaxResult createCapcha() throws IOException, CaptchaException
    {
        // 生成验证码
        String str = producer.createText();
        BufferedImage image = producer.createImage(str);
        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        redisUtils.set(verifyKey, str, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

}