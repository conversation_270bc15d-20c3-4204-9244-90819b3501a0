package com.qf.captcha;



import org.springframework.boot.SpringApplication;
import com.qf.common.security.annotation.EnableCustomConfig;
import com.qf.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;



@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class,HibernateJpaAutoConfiguration.class})
public class CaptchaApplication {

	public static void main(String[] args) {
		SpringApplication.run(CaptchaApplication.class, args);
	}
    
}