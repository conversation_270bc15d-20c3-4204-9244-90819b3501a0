package com.qf.auth.service;



import com.qf.common.core.constant.Constants;
import com.qf.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;



/**
 * 公共管理
 * <AUTHOR>
 */
@Service
public class CommonService  {
	
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 清除登录相关redis
     */
    public void delLoginRedis(String account){
    	redisUtils.del(Constants.FORBID_TIME+account);
    	redisUtils.del(Constants.FORBID_N+account);
    }
    
    
    
    /**
     * 错误密码重复请求登录判断
     * 
     */
    public void getWPassword(String account){
    	Long num = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonNum").toString());
    	Long timeIn = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeIn"));
    	Long timeOut = Long.valueOf(redisUtils.get(Constants.SYS_CONFIG_KEY+"commonTimeOut"));

        if( redisUtils.hasKey(Constants.FORBID_N+account) ){
        	Integer n = Integer.parseInt(redisUtils.get(Constants.FORBID_N+account).toString());
        	if( n>=num ){
        		if( !redisUtils.hasKey(Constants.FORBID_TIME+account) ){
            		// 记录禁用的时间
                    redisUtils.set(Constants.FORBID_TIME+account,"no",timeOut);  
                    // 删除错误次数的记录
                    redisUtils.del(Constants.FORBID_N+account);
        		}
        	}else{
        		// 获取过期时间
        		long expireTime = redisUtils.getExpire(Constants.FORBID_N+account);
                // 刷新错误次数
        		if(expireTime>0){
                    redisUtils.set(Constants.FORBID_N+account,Integer.toString((n+1)),expireTime);   
        		}else{
        			redisUtils.set(Constants.FORBID_N+account,"1",timeIn); 
        		}  
        	}
        }else{
        	// 记录初次的错误
        	redisUtils.set(Constants.FORBID_N+account,"1",timeIn);  
        }
    }
	
	
    
}