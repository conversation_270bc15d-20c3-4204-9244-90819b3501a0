# Tomcat
server: 
  port: 7200

# Spring
spring: 
  application:
    # 应用名称
    name: auth
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 用户名与密码
        #username: nacos
        #password: Xyh789520.
        username: admin
        password: bfqswaq3322112
        # 服务注册地址
        #server-addr: 43.136.132.72:8848
        server-addr: 127.0.0.1:8848
        #server-addr: www.fuzhoubofeng.com:8848
      config:
        # 用户名与密码
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        # 配置中心地址
        #server-addr: 43.136.132.72:8848
        server-addr: 127.0.0.1:8848
        #server-addr: www.fuzhoubofeng.com:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
