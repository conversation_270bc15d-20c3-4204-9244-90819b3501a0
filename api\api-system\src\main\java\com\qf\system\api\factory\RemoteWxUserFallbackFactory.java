package com.qf.system.api.factory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.RemoteWxUserService;
import com.qf.system.api.domain.SysUser;
import feign.hystrix.FallbackFactory;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>   
 */
@Component
public class RemoteWxUserFallbackFactory implements FallbackFactory<RemoteWxUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteWxUserFallbackFactory.class);

    @Override
    public RemoteWxUserService create(Throwable throwable)
    {
        log.error("用户微信服务调用失败:{}", throwable.getMessage());
        return new RemoteWxUserService()
        {
            @Override
            public HttpResult<SysUser> findPageByOpenId(String openid)
            {
                return null;
            }
        };
    }
}
