package com.qf.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import com.qf.common.core.constant.ServiceNameConstants;
import com.qf.common.core.vo.HttpResult;
import com.qf.system.api.domain.SysUser;
import com.qf.system.api.factory.RemoteUpdateUserByIdFallbackFactory;

/**
 * 用户服务
 * 
 * <AUTHOR>   
 */
@FeignClient(contextId = "remoteUpdateUserByIdService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUpdateUserByIdFallbackFactory.class)
public interface RemoteUpdateUserByIdService
{
	
    /**
     * 通过用户名查询用户信息
     *
     * @param str 更新信息
     * @return 结果
     */
	@PostMapping(value = "/user/updateById/{str}")
    public HttpResult<SysUser> updateById(@PathVariable("str") String str);
    
}