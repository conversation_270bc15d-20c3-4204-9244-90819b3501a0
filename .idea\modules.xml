<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/api/api-system/api-system.iml" filepath="$PROJECT_DIR$/api/api-system/api-system.iml" />
      <module fileurl="file://$PROJECT_DIR$/auth/auth.iml" filepath="$PROJECT_DIR$/auth/auth.iml" />
      <module fileurl="file://$PROJECT_DIR$/captcha/captcha.iml" filepath="$PROJECT_DIR$/captcha/captcha.iml" />
      <module fileurl="file://$PROJECT_DIR$/common/common-core/common-core.iml" filepath="$PROJECT_DIR$/common/common-core/common-core.iml" />
      <module fileurl="file://$PROJECT_DIR$/common/common-datascope/common-datascope.iml" filepath="$PROJECT_DIR$/common/common-datascope/common-datascope.iml" />
      <module fileurl="file://$PROJECT_DIR$/common/common-log/common-log.iml" filepath="$PROJECT_DIR$/common/common-log/common-log.iml" />
      <module fileurl="file://$PROJECT_DIR$/common/common-redis/common-redis.iml" filepath="$PROJECT_DIR$/common/common-redis/common-redis.iml" />
      <module fileurl="file://$PROJECT_DIR$/common/common-security/common-security.iml" filepath="$PROJECT_DIR$/common/common-security/common-security.iml" />
      <module fileurl="file://$PROJECT_DIR$/data/data.iml" filepath="$PROJECT_DIR$/data/data.iml" />
      <module fileurl="file://$PROJECT_DIR$/gateway/gateway.iml" filepath="$PROJECT_DIR$/gateway/gateway.iml" />
      <module fileurl="file://$PROJECT_DIR$/services/dev/services-dev.iml" filepath="$PROJECT_DIR$/services/dev/services-dev.iml" />
      <module fileurl="file://$PROJECT_DIR$/services/mobile/services-mobile.iml" filepath="$PROJECT_DIR$/services/mobile/services-mobile.iml" />
      <module fileurl="file://$PROJECT_DIR$/services/monitor/services-monitor.iml" filepath="$PROJECT_DIR$/services/monitor/services-monitor.iml" />
      <module fileurl="file://$PROJECT_DIR$/services/system/services-system.iml" filepath="$PROJECT_DIR$/services/system/services-system.iml" />
      <module fileurl="file://$PROJECT_DIR$/services/task/services-task.iml" filepath="$PROJECT_DIR$/services/task/services-task.iml" />
    </modules>
  </component>
</project>